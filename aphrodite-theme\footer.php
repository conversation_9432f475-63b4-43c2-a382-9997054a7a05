<?php
/**
 * The template for displaying the footer
 *
 * @package Aphrodite
 */

?>

	<footer id="colophon" class="site-footer">
		<div class="container">
			<div class="row">
				<div class="col-3">
					<div class="footer-info">
						<?php if ( has_custom_logo() ) : ?>
							<div class="footer-logo">
								<?php the_custom_logo(); ?>
							</div>
						<?php else : ?>
							<h3 class="footer-title"><?php bloginfo( 'name' ); ?></h3>
						<?php endif; ?>
						<p><?php echo get_theme_mod( 'footer_description', 'Un centro estetico elegante e professionale, dedicato alla tua bellezza e al tuo benessere.' ); ?></p>
						<div class="social-links">
							<?php if ( get_theme_mod( 'social_facebook' ) ) : ?>
								<a href="<?php echo esc_url( get_theme_mod( 'social_facebook' ) ); ?>" class="social-link" target="_blank"><i class="fab fa-facebook-f"></i></a>
							<?php endif; ?>
							<?php if ( get_theme_mod( 'social_instagram' ) ) : ?>
								<a href="<?php echo esc_url( get_theme_mod( 'social_instagram' ) ); ?>" class="social-link" target="_blank"><i class="fab fa-instagram"></i></a>
							<?php endif; ?>
							<?php if ( get_theme_mod( 'social_twitter' ) ) : ?>
								<a href="<?php echo esc_url( get_theme_mod( 'social_twitter' ) ); ?>" class="social-link" target="_blank"><i class="fab fa-twitter"></i></a>
							<?php endif; ?>
						</div>
					</div>
				</div>
				<div class="col-3">
					<h3 class="footer-title"><?php esc_html_e( 'Link Utili', 'aphrodite' ); ?></h3>
					<?php
					wp_nav_menu(
						array(
							'theme_location' => 'footer-menu',
							'menu_class'     => 'footer-links',
							'depth'          => 1,
							'fallback_cb'    => false,
						)
					);
					?>
				</div>
				<div class="col-3">
					<?php if ( is_active_sidebar( 'footer-1' ) ) : ?>
						<div class="footer-widget">
							<?php dynamic_sidebar( 'footer-1' ); ?>
						</div>
					<?php endif; ?>
				</div>
				<div class="col-3">
					<?php if ( is_active_sidebar( 'footer-2' ) ) : ?>
						<div class="footer-widget">
							<?php dynamic_sidebar( 'footer-2' ); ?>
						</div>
					<?php endif; ?>
				</div>
			</div>
			<div class="footer-bottom">
				<p>&copy; <?php echo date_i18n( 'Y' ); ?> <?php bloginfo( 'name' ); ?>. <?php esc_html_e( 'Tutti i diritti riservati.', 'aphrodite' ); ?></p>
			</div>
		</div>
	</footer><!-- #colophon -->
</div><!-- #page -->

<?php wp_footer(); ?>

</body>
</html>
