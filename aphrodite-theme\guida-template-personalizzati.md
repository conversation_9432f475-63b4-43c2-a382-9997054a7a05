# Guida ai Template Personalizzati per Aphrodite Center Store

## Introduzione

Questa guida ti mostrerà come utilizzare i template personalizzati che abbiamo creato per il tuo sito "Aphrodite Center Store". Abbiamo sviluppato template specifici per la home page, la pagina contatti e la pagina servizi, che ti permetteranno di avere un design elegante e coerente su tutto il sito, eliminando i contenuti WordPress predefiniti.

## Template Disponibili

Abbiamo creato tre template personalizzati:

1. **Home Page Personalizzata**: Un layout elegante per la tua pagina iniziale con sezioni per presentare il tuo centro estetico, i servizi principali e le testimonianze dei clienti.
2. **Contatti Personalizzata**: Un template completo per la pagina contatti con informazioni di contatto, orari, mappa, modulo di contatto e FAQ.
3. **Servizi Personalizzata**: Un template dettagliato per presentare tutti i tuoi servizi, organizzati per categorie con descrizioni e prezzi.

## Come Utilizzare i Template Personalizzati

### Passaggio 1: Creare le Pagine

Per ciascun template, dovrai creare una pagina dedicata:

1. Accedi al pannello di amministrazione di WordPress
2. Vai a **Pagine > Aggiungi nuova**
3. Inserisci un titolo per la pagina (es. "Home", "Contatti" o "Servizi")
4. Nel pannello a destra, cerca la sezione **Attributi pagina**
5. Dal menu a tendina **Modello**, seleziona il template desiderato:
   - Per la home page: seleziona **Home Page Personalizzata**
   - Per la pagina contatti: seleziona **Contatti Personalizzata**
   - Per la pagina servizi: seleziona **Servizi Personalizzata**
6. Clicca su **Pubblica**

### Passaggio 2: Impostare la Home Page

Per impostare la tua home page personalizzata come pagina iniziale del sito:

1. Vai a **Impostazioni > Lettura**
2. Seleziona l'opzione **Una pagina statica**
3. Dal menu a tendina **Pagina iniziale**, seleziona la pagina con il template "Home Page Personalizzata"
4. Clicca su **Salva modifiche**

### Passaggio 3: Configurare il Menu di Navigazione

Per garantire che i visitatori possano navigare facilmente tra le tue pagine personalizzate:

1. Vai a **Aspetto > Menu**
2. Crea un nuovo menu o modifica quello esistente
3. Aggiungi le pagine che hai creato (Home, Servizi, Contatti)
4. Nella sezione **Posizione menu**, seleziona **Menu principale**
5. Clicca su **Salva menu**

## Personalizzazione dei Template

### Home Page Personalizzata

Il template della home page include diverse sezioni che puoi personalizzare:

- **Hero Section**: Presenta il tuo centro estetico con un titolo accattivante e una breve descrizione
- **Chi Siamo**: Racconta la storia e la filosofia del tuo centro
- **Servizi in Evidenza**: Mostra i tuoi servizi principali
- **Testimonianze**: Condividi le opinioni dei tuoi clienti soddisfatti
- **Offerta Speciale**: Promuovi offerte o pacchetti speciali
- **Contatti**: Fornisci informazioni di contatto rapide

Per personalizzare i contenuti, puoi:
1. Modificare direttamente il file template-home.php
2. Utilizzare campi personalizzati con plugin come Advanced Custom Fields
3. Utilizzare Elementor per modificare visivamente il layout (richiede Elementor Pro)

### Contatti Personalizzata

Il template della pagina contatti include:

- **Informazioni di Contatto**: Indirizzo, telefono, email
- **Orari di Apertura**: Quando i clienti possono trovarti
- **Modulo di Contatto**: Per ricevere messaggi dai clienti
- **Mappa**: Per mostrare la tua posizione
- **FAQ**: Risposte alle domande frequenti

Per il modulo di contatto, è necessario:
1. Installare il plugin Contact Form 7
2. Creare un modulo con i campi necessari
3. Inserire lo shortcode del modulo nel template

Per la mappa, puoi:
1. Installare un plugin per mappe come WP Google Maps
2. Creare una mappa con la tua posizione
3. Inserire lo shortcode della mappa nel template

### Servizi Personalizzata

Il template della pagina servizi è organizzato in categorie:

- **Trattamenti Viso**
- **Trattamenti Corpo**
- **Mani e Piedi**
- **Massaggi**
- **Epilazione**
- **Pacchetti Benessere**

Per personalizzare i servizi:
1. Modifica le descrizioni e i prezzi nel file template-servizi.php
2. Sostituisci le immagini delle categorie nella cartella assets/images
3. Aggiungi o rimuovi servizi secondo le tue esigenze

## Gestione delle Immagini

Per ottenere il massimo impatto visivo, è importante aggiungere immagini di alta qualità:

1. Prepara le seguenti immagini:
   - Immagini per la home page (hero, chi siamo, servizi, testimonial, promo)
   - Immagini per le categorie di servizi (6 immagini, dimensioni consigliate: 400x300px)
   - Immagine gift card (dimensioni consigliate: 600x400px)
   - Logo per header e footer

2. Carica queste immagini nella cartella `/wp-content/themes/aphrodite-theme/assets/images/` utilizzando un client FTP, o sostituisci le immagini predefinite con lo stesso nome.

## Personalizzazione Avanzata

Se desideri personalizzare ulteriormente i template:

1. **CSS Personalizzato**: Modifica i file CSS nella cartella assets/css
2. **JavaScript Personalizzato**: Modifica i file JS nella cartella assets/js
3. **Struttura Template**: Modifica i file PHP nella cartella page-templates

## Risoluzione Problemi

### I template non appaiono nel menu a tendina

- Verifica che il tema "Aphrodite" sia attivato
- Controlla che i file template siano presenti nella cartella `/wp-content/themes/aphrodite-theme/page-templates/`
- Prova a disattivare e riattivare il tema

### Le immagini non vengono visualizzate

- Verifica che le immagini siano state caricate nella cartella corretta
- Controlla che i nomi dei file corrispondano a quelli specificati nei template
- Assicurati che le immagini abbiano i permessi di lettura corretti (644 o 755)

### Il modulo di contatto non funziona

- Verifica che Contact Form 7 sia installato e attivato
- Controlla che l'ID del modulo nel shortcode corrisponda all'ID effettivo del modulo
- Testa il modulo inviando un messaggio di prova

## Conclusione

Seguendo questa guida, avrai configurato pagine personalizzate per il tuo centro estetico "Aphrodite Center Store", eliminando i contenuti WordPress predefiniti e utilizzando invece layout eleganti progettati specificamente per te.

Se hai bisogno di ulteriore assistenza, non esitare a contattarci.
