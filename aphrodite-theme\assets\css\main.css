/**
 * Aphrodite Center Store - Tema CSS Personalizzato
 * Tema per centro estetico con colori principali oro chiaro e bianco
 * Compatibile con Elementor (versione gratuita)
 */

/* ======================================
   VARIABILI CSS
====================================== */
:root {
    /* Colori Principali */
    --oro-chiaro: #D4AF37;
    --bianco: #FFFFFF;
    
    /* Colori Complementari */
    --oro-scuro: #B8860B;
    --beige-chiaro: #F5F5DC;
    --grigio-chiaro: #F8F8F8;
    --grigio-scuro: #333333;
    --nero-soft: #222222;
    
    /* Spaziature */
    --spacing-xs: 5px;
    --spacing-sm: 10px;
    --spacing-md: 20px;
    --spacing-lg: 40px;
    --spacing-xl: 80px;
    
    /* Border Radius */
    --radius-sm: 3px;
    --radius-md: 5px;
    --radius-lg: 10px;
    
    /* Ombre */
    --shadow-soft: 0 5px 15px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 8px 30px rgba(0, 0, 0, 0.1);
    --shadow-hard: 0 10px 50px rgba(0, 0, 0, 0.15);
}

/* ======================================
   RESET E NORMALIZZAZIONE
====================================== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Open Sans', sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: var(--grigio-scuro);
    background-color: var(--bianco);
}

/* ======================================
   TIPOGRAFIA
====================================== */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    margin-bottom: var(--spacing-md);
    color: var(--nero-soft);
    font-weight: 700;
    line-height: 1.2;
}

h1 {
    font-size: 48px;
}

h2 {
    font-size: 36px;
}

h3 {
    font-size: 30px;
}

h4 {
    font-size: 24px;
}

h5 {
    font-size: 20px;
}

h6 {
    font-size: 18px;
}

p {
    margin-bottom: var(--spacing-md);
}

.subtitle {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    color: var(--oro-chiaro);
}

.accent-text {
    font-family: 'Great Vibes', cursive;
    color: var(--oro-chiaro);
    font-size: 28px;
    line-height: 1.4;
}

.text-light {
    font-weight: 300;
}

.text-center {
    text-align: center;
}

/* ======================================
   PULSANTI
====================================== */
.btn {
    display: inline-block;
    padding: 12px 30px;
    border-radius: var(--radius-md);
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 16px;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
}

.btn-primary {
    background-color: var(--oro-chiaro);
    color: var(--bianco);
}

.btn-primary:hover {
    background-color: var(--oro-scuro);
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    background-color: transparent;
    color: var(--oro-chiaro);
    border: 2px solid var(--oro-chiaro);
}

.btn-secondary:hover {
    background-color: var(--oro-chiaro);
    color: var(--bianco);
}

.btn-large {
    padding: 15px 40px;
    font-size: 18px;
}

.btn-small {
    padding: 8px 20px;
    font-size: 14px;
}

/* ======================================
   HEADER E NAVIGAZIONE
====================================== */
.site-header {
    background-color: var(--bianco);
    box-shadow: var(--shadow-soft);
    padding: 15px 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.logo {
    max-height: 80px;
    width: auto;
}

.main-navigation {
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.main-navigation ul {
    list-style: none;
    display: flex;
    margin: 0;
    padding: 0;
}

.main-navigation li {
    margin-left: var(--spacing-md);
}

.main-navigation a {
    text-decoration: none;
    color: var(--grigio-scuro);
    font-family: 'Montserrat', sans-serif;
    font-weight: 500;
    font-size: 16px;
    transition: color 0.3s ease;
    position: relative;
    padding: 5px 0;
}

.main-navigation a:hover,
.main-navigation a.active {
    color: var(--oro-chiaro);
}

.main-navigation a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: var(--oro-chiaro);
    transition: width 0.3s ease;
}

.main-navigation a:hover::after,
.main-navigation a.active::after {
    width: 100%;
}

/* ======================================
   HERO SECTION
====================================== */
.hero-section {
    background-color: var(--beige-chiaro);
    padding: var(--spacing-xl) 0;
    position: relative;
    overflow: hidden;
}

.hero-content {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
    position: relative;
    z-index: 2;
}

.hero-title {
    margin-bottom: var(--spacing-md);
    color: var(--nero-soft);
}

.hero-subtitle {
    font-family: 'Great Vibes', cursive;
    font-size: 32px;
    color: var(--oro-chiaro);
    margin-bottom: var(--spacing-md);
}

.hero-description {
    margin-bottom: var(--spacing-lg);
}

.hero-buttons {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
}

.hero-image {
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
}

/* ======================================
   SEZIONI GENERALI
====================================== */
.section {
    padding: var(--spacing-xl) 0;
}

.section-title {
    text-align: center;
    margin-bottom: var(--spacing-lg);
    position: relative;
}

.section-title::after {
    content: '';
    display: block;
    width: 80px;
    height: 3px;
    background-color: var(--oro-chiaro);
    margin: var(--spacing-sm) auto 0;
}

.section-subtitle {
    text-align: center;
    font-family: 'Great Vibes', cursive;
    font-size: 28px;
    color: var(--oro-chiaro);
    margin-bottom: var(--spacing-lg);
}

.section-bg-light {
    background-color: var(--grigio-chiaro);
}

.section-bg-gold {
    background-color: var(--oro-chiaro);
    color: var(--bianco);
}

.section-bg-gold .section-title,
.section-bg-gold h2,
.section-bg-gold h3,
.section-bg-gold h4 {
    color: var(--bianco);
}

.section-bg-gold .section-title::after {
    background-color: var(--bianco);
}

/* ======================================
   CARD E SERVIZI
====================================== */
.card {
    background-color: var(--bianco);
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-soft);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-bottom: var(--spacing-md);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.card-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.card-content {
    padding: var(--spacing-md);
}

.card-title {
    font-size: 22px;
    margin-bottom: var(--spacing-sm);
}

.card-price {
    color: var(--oro-chiaro);
    font-weight: 700;
    font-size: 20px;
    margin-bottom: var(--spacing-sm);
}

.service-item {
    display: flex;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid rgba(212, 175, 55, 0.2);
}

.service-icon {
    flex: 0 0 60px;
    height: 60px;
    background-color: var(--oro-chiaro);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--spacing-md);
    color: var(--bianco);
    font-size: 24px;
}

.service-content {
    flex: 1;
}

.service-title {
    margin-bottom: var(--spacing-xs);
    font-size: 20px;
}

.service-price {
    color: var(--oro-chiaro);
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
}

/* ======================================
   TESTIMONIALS
====================================== */
.testimonial {
    background-color: var(--bianco);
    padding: var(--spacing-lg);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-soft);
    margin-bottom: var(--spacing-md);
    position: relative;
}

.testimonial::before {
    content: '"';
    font-family: 'Playfair Display', serif;
    font-size: 120px;
    color: rgba(212, 175, 55, 0.1);
    position: absolute;
    top: -20px;
    left: 20px;
    line-height: 1;
}

.testimonial-content {
    font-style: italic;
    margin-bottom: var(--spacing-md);
    position: relative;
    z-index: 2;
}

.testimonial-author {
    display: flex;
    align-items: center;
}

.testimonial-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: var(--spacing-sm);
    border: 3px solid var(--oro-chiaro);
}

.testimonial-name {
    font-weight: 700;
    margin-bottom: 0;
}

.testimonial-role {
    color: var(--oro-chiaro);
    font-size: 14px;
}

.testimonial-rating {
    color: var(--oro-chiaro);
    font-size: 18px;
    margin-bottom: var(--spacing-xs);
}

/* ======================================
   FORM E CONTATTI
====================================== */
.contact-form {
    background-color: var(--bianco);
    padding: var(--spacing-lg);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-soft);
}

.form-group {
    margin-bottom: var(--spacing-md);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 600;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: var(--radius-sm);
    font-family: 'Open Sans', sans-serif;
    font-size: 16px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--oro-chiaro);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.2);
}

textarea.form-control {
    min-height: 150px;
    resize: vertical;
}

.contact-info {
    margin-bottom: var(--spacing-lg);
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.contact-icon {
    flex: 0 0 40px;
    height: 40px;
    background-color: var(--oro-chiaro);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--spacing-md);
    color: var(--bianco);
    font-size: 18px;
}

.contact-text {
    flex: 1;
}

.contact-text h4 {
    margin-bottom: var(--spacing-xs);
}

/* ======================================
   FOOTER
====================================== */
.site-footer {
    background-color: var(--nero-soft);
    color: var(--grigio-chiaro);
    padding: var(--spacing-lg) 0;
}

.footer-logo {
    max-height: 80px;
    width: auto;
    margin-bottom: var(--spacing-md);
}

.footer-title {
    color: var(--bianco);
    font-size: 20px;
    margin-bottom: var(--spacing-md);
    position: relative;
    padding-bottom: var(--spacing-sm);
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background-color: var(--oro-chiaro);
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: var(--spacing-sm);
}

.footer-links a {
    color: var(--grigio-chiaro);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--oro-chiaro);
}

.social-links {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: var(--bianco);
    transition: all 0.3s ease;
}

.social-link:hover {
    background-color: var(--oro-chiaro);
    transform: translateY(-3px);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: var(--spacing-md);
    margin-top: var(--spacing-lg);
    text-align: center;
    font-size: 14px;
}

/* ======================================
   UTILITY CLASSES
====================================== */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col {
    flex: 1;
    padding: 0 15px;
}

.col-2 {
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 15px;
}

.col-3 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
    padding: 0 15px;
}

.col-4 {
    flex: 0 0 25%;
    max-width: 25%;
    padding: 0 15px;
}

.mb-0 {
    margin-bottom: 0 !important;
}

.mb-1 {
    margin-bottom: var(--spacing-xs) !important;
}

.mb-2 {
    margin-bottom: var(--spacing-sm) !important;
}

.mb-3 {
    margin-bottom: var(--spacing-md) !important;
}

.mb-4 {
    margin-bottom: var(--spacing-lg) !important;
}

.mb-5 {
    margin-bottom: var(--spacing-xl) !important;
}

.mt-0 {
    margin-top: 0 !important;
}

.mt-1 {
    margin-top: var(--spacing-xs) !important;
}

.mt-2 {
    margin-top: var(--spacing-sm) !important;
}

.mt-3 {
    margin-top: var(--spacing-md) !important;
}

.mt-4 {
    margin-top: var(--spacing-lg) !important;
}

.mt-5 {
    margin-top: var(--spacing-xl) !important;
}

.text-gold {
    color: var(--oro-chiaro) !important;
}

.text-white {
    color: var(--bianco) !important;
}

.bg-gold {
    background-color: var(--oro-chiaro) !important;
}

.bg-white {
    background-color: var(--bianco) !important;
}

.bg-light {
    background-color: var(--grigio-chiaro) !important;
}

.d-flex {
    display: flex !important;
}

.align-center {
    align-items: center !important;
}

.justify-center {
    justify-content: center !important;
}

.justify-between {
    justify-content: space-between !important;
}

.text-center {
    text-align: center !important;
}

.text-right {
    text-align: right !important;
}

.text-left {
    text-align: left !important;
}

.w-100 {
    width: 100% !important;
}

.h-100 {
    height: 100% !important;
}

/* ======================================
   ANIMAZIONI
====================================== */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.fade-in {
    animation: fadeIn 1s ease-in-out;
}

@keyframes slideUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.slide-up {
    animation: slideUp 0.8s ease-out;
}

/* ======================================
   MEDIA QUERIES
====================================== */
@media (max-width: 1200px) {
    .container {
        max-width: 992px;
    }
}

@media (max-width: 992px) {
    .container {
        max-width: 768px;
    }
    
    h1 {
        font-size: 40px;
    }
    
    h2 {
        font-size: 32px;
    }
    
    .col-3, .col-4 {
        flex: 0 0 50%;
        max-width: 50%;
    }
}

@media (max-width: 768px) {
    .container {
        max-width: 576px;
    }
    
    h1 {
        font-size: 36px;
    }
    
    h2 {
        font-size: 28px;
    }
    
    .col-2, .col-3, .col-4 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .hero-image {
        position: relative;
        width: 100%;
        height: 300px;
        margin-top: var(--spacing-md);
    }
    
    .hero-content {
        max-width: 100%;
    }
    
    .hero-buttons {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .main-navigation {
        display: none;
    }
}

@media (max-width: 576px) {
    h1 {
        font-size: 32px;
    }
    
    h2 {
        font-size: 24px;
    }
    
    .section {
        padding: var(--spacing-lg) 0;
    }
    
    .testimonial {
        padding: var(--spacing-md);
    }
}

/* ======================================
   ELEMENTOR COMPATIBILITY
====================================== */
/* Elementor Section */
.elementor-section {
    padding: var(--spacing-lg) 0;
}

/* Elementor Heading */
.elementor-heading-title {
    font-family: 'Playfair Display', serif !important;
    color: var(--nero-soft) !important;
}

/* Elementor Button */
.elementor-button {
    background-color: var(--oro-chiaro) !important;
    color: var(--bianco) !important;
    font-family: 'Montserrat', sans-serif !important;
    border-radius: var(--radius-md) !important;
    transition: all 0.3s ease !important;
}

.elementor-button:hover {
    background-color: var(--oro-scuro) !important;
    box-shadow: var(--shadow-medium) !important;
}

/* Elementor Icon */
.elementor-icon {
    color: var(--oro-chiaro) !important;
}

/* Elementor Icon Box */
.elementor-icon-box-title {
    font-family: 'Playfair Display', serif !important;
    color: var(--nero-soft) !important;
}

.elementor-icon-box-description {
    font-family: 'Open Sans', sans-serif !important;
    color: var(--grigio-scuro) !important;
}

/* Elementor Divider */
.elementor-divider {
    --divider-color: var(--oro-chiaro) !important;
}

/* Elementor Form */
.elementor-field-group .elementor-field {
    border: 1px solid #ddd !important;
    border-radius: var(--radius-sm) !important;
    padding: 12px 15px !important;
    transition: border-color 0.3s ease, box-shadow 0.3s ease !important;
}

.elementor-field-group .elementor-field:focus {
    border-color: var(--oro-chiaro) !important;
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.2) !important;
}

.elementor-field-type-submit .elementor-button {
    background-color: var(--oro-chiaro) !important;
    color: var(--bianco) !important;
}

/* Elementor Testimonial */
.elementor-testimonial-content {
    font-style: italic !important;
}

.elementor-testimonial-name {
    color: var(--nero-soft) !important;
    font-weight: 700 !important;
}

.elementor-testimonial-job {
    color: var(--oro-chiaro) !important;
}

/* Elementor Accordion */
.elementor-accordion .elementor-accordion-item {
    border-color: rgba(212, 175, 55, 0.2) !important;
}

.elementor-accordion .elementor-tab-title {
    color: var(--nero-soft) !important;
}

.elementor-accordion .elementor-tab-title.elementor-active {
    color: var(--oro-chiaro) !important;
}

/* Elementor Tabs */
.elementor-tabs .elementor-tab-title {
    color: var(--grigio-scuro) !important;
}

.elementor-tabs .elementor-tab-title.elementor-active {
    color: var(--oro-chiaro) !important;
}

.elementor-tabs .elementor-tab-content {
    border-color: rgba(212, 175, 55, 0.2) !important;
}

/* Elementor Price Table */
.elementor-price-table .elementor-price-table__header {
    background-color: var(--oro-chiaro) !important;
}

.elementor-price-table .elementor-price-table__price {
    color: var(--nero-soft) !important;
}

.elementor-price-table .elementor-price-table__button {
    background-color: var(--oro-chiaro) !important;
}

/* Elementor Image Box */
.elementor-image-box-title {
    font-family: 'Playfair Display', serif !important;
    color: var(--nero-soft) !important;
}

.elementor-image-box-description {
    font-family: 'Open Sans', sans-serif !important;
    color: var(--grigio-scuro) !important;
}

/* --- Miglioramenti contatti e FAQ --- */
.contact-card {
    margin: 0 auto 40px auto;
    max-width: 400px;
    box-shadow: 0 8px 32px rgba(33,140,116,0.08);
}
.contact-card .contact-icon {
    width: 60px;
    height: 60px;
    border: 2px solid var(--oro-chiaro);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    color: var(--oro-chiaro);
    margin-bottom: 10px;
}
.contact-hours td {
    padding: 6px 0;
    font-size: 17px;
}
.contact-hours tr:first-child td {
    font-weight: bold;
    color: var(--oro-chiaro);
}

/* Modern Accordion FAQ */
.modern-accordion .accordion-item {
    border-radius: 10px;
    margin-bottom: 18px;
    box-shadow: 0 2px 12px rgba(33,140,116,0.07);
    background: #fff;
    overflow: hidden;
    border: none;
}
.modern-accordion .accordion-header {
    background: var(--grigio-chiaro);
    cursor: pointer;
    padding: 18px 24px;
    font-size: 18px;
    font-weight: 600;
    color: var(--oro-chiaro);
    border: none;
    outline: none;
    transition: background 0.2s;
}
.modern-accordion .accordion-header:hover {
    background: #f9f6ef;
}
.modern-accordion .accordion-body {
    padding: 18px 24px;
    background: #fff;
    color: var(--grigio-scuro);
    font-size: 16px;
    border-top: 1px solid #eee;
}

@media (max-width: 992px) {
    .contact-card { max-width: 100%; }
    .col-2 { flex: 0 0 100%; max-width: 100%; }
}

/* --- MENU MOBILE HAMBURGER --- */
.mobile-hamburger {
    display: none;
    font-size: 2.2rem;
    color: var(--oro-chiaro);
    margin-left: 20px;
    cursor: pointer;
    z-index: 120;
}
@media (max-width: 992px) {
    .main-navigation ul {
        display: none;
        position: absolute;
        top: 70px;
        right: 0;
        background: #fff;
        box-shadow: 0 8px 32px rgba(33,140,116,0.08);
        border-radius: 0 0 10px 10px;
        width: 220px;
        flex-direction: column;
        padding: 20px 0;
        z-index: 110;
    }
    .main-navigation.open ul {
        display: flex;
    }
    .main-navigation ul li {
        margin: 0 0 12px 0;
        text-align: right;
        padding: 0 24px;
    }
    .main-navigation ul li:last-child {
        margin-bottom: 0;
    }
    .mobile-hamburger {
        display: block;
    }
    .main-navigation {
        position: relative;
    }
}
@media (max-width: 576px) {
    .main-navigation ul {
        width: 100vw;
        right: -15px;
        top: 60px;
        border-radius: 0 0 10px 10px;
    }
}
