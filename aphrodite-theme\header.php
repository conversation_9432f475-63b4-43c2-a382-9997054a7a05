<?php
/**
 * The header for our theme
 *
 * @package Aphrodite
 */

?>
<!doctype html>
<html <?php language_attributes(); ?>>
<head>
	<meta charset="<?php bloginfo( 'charset' ); ?>">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="profile" href="https://gmpg.org/xfn/11">

	<?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>
<div id="page" class="site">
	<a class="skip-link screen-reader-text" href="#primary"><?php esc_html_e( 'Skip to content', 'aphrodite' ); ?></a>

	<header id="masthead" class="site-header">
		<div class="container">
			<div class="row align-center justify-between">
				<div class="site-branding">
					<?php
					the_custom_logo();
					if ( is_front_page() && is_home() ) :
						?>
						<h1 class="site-title"><a href="<?php echo esc_url( home_url( '/' ) ); ?>" rel="home"><?php bloginfo( 'name' ); ?></a></h1>
						<?php
					else :
						?>
						<p class="site-title"><a href="<?php echo esc_url( home_url( '/' ) ); ?>" rel="home"><?php bloginfo( 'name' ); ?></a></p>
						<?php
					endif;
					$aphrodite_description = get_bloginfo( 'description', 'display' );
					if ( $aphrodite_description || is_customize_preview() ) :
						?>
						<p class="site-description"><?php echo $aphrodite_description; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped ?></p>
					<?php endif; ?>
				</div><!-- .site-branding -->

				<nav id="site-navigation" class="main-navigation">
					<a href="#" class="mobile-hamburger" id="mobile-hamburger" aria-label="Apri menu"><i class="fas fa-bars"></i></a>
					<?php
					wp_nav_menu(
						array(
							'theme_location' => 'menu-1',
							'menu_id'        => 'primary-menu',
							'container'      => false,
							'fallback_cb'    => function() {
								echo '<ul>
									<li><a href="https://aphroditecenter.store/" class="active">Home</a></li>
									<li><a href="https://aphroditecenter.store/trattamenti/">Servizi</a></li>
									<li><a href="https://aphroditecenter.store/contatti/">Contatti</a></li>';
									if (current_user_can('edit_pages')) {
										echo '<li><a href="https://aphroditecenter.store/gestionale-bacoli/">Gestionale Bacoli</a></li>
										<li><a href="https://aphroditecenter.store/gestionale-monte-di-procida/">Gestionale Monte di Procida</a></li>';
									}
								echo '</ul>';
							},
						)
					);
					?>
				</nav><!-- #site-navigation -->
			</div>
		</div>
	</header><!-- #masthead -->
