<?php
/**
 * Aphrodite Theme functions and definitions
 *
 * @package Aphrodite
 */

if ( ! defined( 'APHRODITE_VERSION' ) ) {
    // Replace the version number of the theme on each release.
    define( 'APHRODITE_VERSION', '1.0.0' );
}

/**
 * Sets up theme defaults and registers support for various WordPress features.
 */
function aphrodite_setup() {
    /*
     * Make theme available for translation.
     * Translations can be filed in the /languages/ directory.
     */
    load_theme_textdomain( 'aphrodite', get_template_directory() . '/languages' );

    // Add default posts and comments RSS feed links to head.
    add_theme_support( 'automatic-feed-links' );

    /*
     * Let WordPress manage the document title.
     * By adding theme support, we declare that this theme does not use a
     * hard-coded <title> tag in the document head, and expect WordPress to
     * provide it for us.
     */
    add_theme_support( 'title-tag' );

    /*
     * Enable support for Post Thumbnails on posts and pages.
     *
     * @link https://developer.wordpress.org/themes/functionality/featured-images-post-thumbnails/
     */
    add_theme_support( 'post-thumbnails' );

    // This theme uses wp_nav_menu() in one location.
    register_nav_menus(
        array(
            'menu-1' => esc_html__( 'Primary', 'aphrodite' ),
            'footer-menu' => esc_html__( 'Footer Menu', 'aphrodite' ),
        )
    );

    /*
     * Switch default core markup for search form, comment form, and comments
     * to output valid HTML5.
     */
    add_theme_support(
        'html5',
        array(
            'search-form',
            'comment-form',
            'comment-list',
            'gallery',
            'caption',
            'style',
            'script',
        )
    );

    // Set up the WordPress core custom background feature.
    add_theme_support(
        'custom-background',
        apply_filters(
            'aphrodite_custom_background_args',
            array(
                'default-color' => 'ffffff',
                'default-image' => '',
            )
        )
    );

    // Add theme support for selective refresh for widgets.
    add_theme_support( 'customize-selective-refresh-widgets' );

    /**
     * Add support for core custom logo.
     *
     * @link https://codex.wordpress.org/Theme_Logo
     */
    add_theme_support(
        'custom-logo',
        array(
            'height'      => 250,
            'width'       => 250,
            'flex-width'  => true,
            'flex-height' => true,
        )
    );
}
add_action( 'after_setup_theme', 'aphrodite_setup' );

/**
 * Set the content width in pixels, based on the theme's design and stylesheet.
 *
 * Priority 0 to make it available to lower priority callbacks.
 *
 * @global int $content_width
 */
function aphrodite_content_width() {
    $GLOBALS['content_width'] = apply_filters( 'aphrodite_content_width', 1200 );
}
add_action( 'after_setup_theme', 'aphrodite_content_width', 0 );

/**
 * Register widget area.
 *
 * @link https://developer.wordpress.org/themes/functionality/sidebars/#registering-a-sidebar
 */
function aphrodite_widgets_init() {
    register_sidebar(
        array(
            'name'          => esc_html__( 'Footer 1', 'aphrodite' ),
            'id'            => 'footer-1',
            'description'   => esc_html__( 'Add footer widgets here.', 'aphrodite' ),
            'before_widget' => '<section id="%1$s" class="widget %2$s">',
            'after_widget'  => '</section>',
            'before_title'  => '<h2 class="widget-title">',
            'after_title'   => '</h2>',
        )
    );
    
    register_sidebar(
        array(
            'name'          => esc_html__( 'Footer 2', 'aphrodite' ),
            'id'            => 'footer-2',
            'description'   => esc_html__( 'Add footer widgets here.', 'aphrodite' ),
            'before_widget' => '<section id="%1$s" class="widget %2$s">',
            'after_widget'  => '</section>',
            'before_title'  => '<h2 class="widget-title">',
            'after_title'   => '</h2>',
        )
    );
    
    register_sidebar(
        array(
            'name'          => esc_html__( 'Footer 3', 'aphrodite' ),
            'id'            => 'footer-3',
            'description'   => esc_html__( 'Add footer widgets here.', 'aphrodite' ),
            'before_widget' => '<section id="%1$s" class="widget %2$s">',
            'after_widget'  => '</section>',
            'before_title'  => '<h2 class="widget-title">',
            'after_title'   => '</h2>',
        )
    );
}
add_action( 'widgets_init', 'aphrodite_widgets_init' );

/**
 * Enqueue scripts and styles.
 */
function aphrodite_scripts() {
    // Enqueue Google Fonts
    wp_enqueue_style( 'aphrodite-google-fonts', 'https://fonts.googleapis.com/css2?family=Great+Vibes&family=Montserrat:wght@400;500;600;700&family=Open+Sans:wght@300;400;600;700&family=Playfair+Display:wght@400;700&display=swap', array(), null );
    
    // Enqueue main stylesheet
    wp_enqueue_style( 'aphrodite-style', get_stylesheet_uri(), array(), APHRODITE_VERSION );
    
    // Enqueue custom stylesheets
    wp_enqueue_style( 'aphrodite-main', get_template_directory_uri() . '/assets/css/main.css', array(), APHRODITE_VERSION );
    wp_enqueue_style( 'aphrodite-additional', get_template_directory_uri() . '/assets/css/additional.css', array(), APHRODITE_VERSION );
    
    // Enqueue custom scripts
    wp_enqueue_script( 'aphrodite-navigation', get_template_directory_uri() . '/assets/js/navigation.js', array(), APHRODITE_VERSION, true );
    wp_enqueue_script( 'aphrodite-custom', get_template_directory_uri() . '/assets/js/custom.js', array('jquery'), APHRODITE_VERSION, true );

    if ( is_singular() && comments_open() && get_option( 'thread_comments' ) ) {
        wp_enqueue_script( 'comment-reply' );
    }
}
add_action( 'wp_enqueue_scripts', 'aphrodite_scripts' );

/**
 * Custom template tags for this theme.
 */
require get_template_directory() . '/inc/template-tags.php';

/**
 * Functions which enhance the theme by hooking into WordPress.
 */
require get_template_directory() . '/inc/template-functions.php';

/**
 * Customizer additions.
 */
require get_template_directory() . '/inc/customizer.php';

/**
 * Load Jetpack compatibility file.
 */
if ( defined( 'JETPACK__VERSION' ) ) {
    require get_template_directory() . '/inc/jetpack.php';
}

/**
 * Load WooCommerce compatibility file.
 */
if ( class_exists( 'WooCommerce' ) ) {
    require get_template_directory() . '/inc/woocommerce.php';
}

/**
 * Elementor compatibility
 */
function aphrodite_elementor_support() {
    // Add support for Elementor
    add_theme_support( 'elementor' );
}
add_action( 'after_setup_theme', 'aphrodite_elementor_support' );
