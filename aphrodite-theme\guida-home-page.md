# Guida all'Impostazione della Home Page Personalizzata

## Introduzione

Questa guida ti mostrerà come configurare la home page personalizzata per il tuo sito "Aphrodite Center Store", eliminando i contenuti WordPress predefiniti (post recenti, commenti, ecc.) e utilizzando invece il layout elegante che abbiamo progettato specificamente per il tuo centro estetico.

## Passaggi per Configurare la Home Page Personalizzata

### 1. Creare una Nuova Pagina

1. Accedi al pannello di amministrazione di WordPress
2. Vai a **Pagine > Aggiungi nuova**
3. Inserisci un titolo per la pagina (es. "Home" o "Pagina Iniziale")
4. Nel pannello a destra, cerca la sezione **Attributi pagina**
5. Dal menu a tendina **Modello**, seleziona **Home Page Personalizzata**
6. Clicca su **Pubblica**

### 2. Impostare la Pagina come Home Page Statica

1. Vai a **Impostazioni > Lettura**
2. Seleziona l'opzione **Una pagina statica**
3. Dal menu a tendina **Pagina iniziale**, seleziona la pagina che hai appena creato
4. Clicca su **Salva modifiche**

### 3. Configurare il Logo

1. Vai a **Aspetto > Personalizza**
2. Clicca su **Identità del sito**
3. Clicca su **Seleziona logo**
4. Carica il logo del tuo centro estetico (preferibilmente con sfondo trasparente)
5. Regola le dimensioni e clicca su **Seleziona**
6. Clicca su **Pubblica** per salvare le modifiche

### 4. Configurare i Menu

1. Vai a **Aspetto > Menu**
2. Crea un nuovo menu o modifica quello esistente
3. Aggiungi le pagine che desideri nel menu (Home, Servizi, Recensioni, Contatti, ecc.)
4. Nella sezione **Posizione menu**, seleziona **Menu principale**
5. Clicca su **Salva menu**
6. Ripeti il processo per creare un menu per il footer, selezionando **Menu footer** come posizione

### 5. Aggiungere le Immagini

Per ottenere il massimo impatto visivo, è importante aggiungere immagini di alta qualità:

1. Prepara le seguenti immagini:
   - Immagine hero (dimensioni consigliate: 1200x800px)
   - Immagine "Chi Siamo" (dimensioni consigliate: 600x800px)
   - Immagini servizi (3 immagini, dimensioni consigliate: 400x300px)
   - Immagini testimonial (2 immagini, dimensioni consigliate: 100x100px, preferibilmente rotonde)
   - Immagine promo (dimensioni consigliate: 600x400px)
   - Logo bianco per il footer (con sfondo trasparente)

2. Carica queste immagini nella cartella `/wp-content/themes/aphrodite-theme/assets/images/` utilizzando un client FTP, o sostituisci le immagini predefinite con lo stesso nome:
   - hero-default.jpg
   - about-default.jpg
   - service1.jpg, service2.jpg, service3.jpg
   - client1.jpg, client2.jpg
   - promo.jpg
   - logo.png (per l'header)
   - logo-white.png (per il footer)

### 6. Configurare il Modulo di Contatto

Per far funzionare il modulo di contatto:

1. Installa e attiva il plugin **Contact Form 7**
2. Vai a **Contatti > Aggiungi nuovo**
3. Crea un nuovo modulo con i campi Nome, Email, Telefono e Messaggio
4. Assegna l'ID "contact-form" al modulo
5. Copia lo shortcode generato (es. `[contact-form-7 id="123" title="Modulo di contatto"]`)
6. Modifica il file `/wp-content/themes/aphrodite-theme/page-templates/template-home.php`
7. Trova la riga con `<?php echo do_shortcode('[contact-form-7 id="contact-form" title="Modulo di contatto"]'); ?>`
8. Sostituisci con lo shortcode che hai copiato

### 7. Personalizzare i Social Media

1. Vai a **Aspetto > Personalizza**
2. Clicca su **Social Media**
3. Inserisci gli URL dei tuoi profili social (Facebook, Instagram, Twitter)
4. Clicca su **Pubblica** per salvare le modifiche

## Risoluzione Problemi

### Il template "Home Page Personalizzata" non appare nel menu a tendina

- Verifica che il tema "Aphrodite" sia attivato
- Controlla che il file `template-home.php` sia presente nella cartella `/wp-content/themes/aphrodite-theme/page-templates/`
- Prova a disattivare e riattivare il tema

### Le immagini non vengono visualizzate

- Verifica che le immagini siano state caricate nella cartella corretta
- Controlla che i nomi dei file corrispondano a quelli specificati nel template
- Assicurati che le immagini abbiano i permessi di lettura corretti (644 o 755)

### Il modulo di contatto non funziona

- Verifica che Contact Form 7 sia installato e attivato
- Controlla che l'ID del modulo nel shortcode corrisponda all'ID effettivo del modulo
- Testa il modulo inviando un messaggio di prova

## Conclusione

Seguendo questa guida, avrai configurato una home page personalizzata per il tuo centro estetico "Aphrodite Center Store", eliminando i contenuti WordPress predefiniti e utilizzando invece il layout elegante che abbiamo progettato specificamente per te.

Se hai bisogno di ulteriore assistenza, non esitare a contattarci.
