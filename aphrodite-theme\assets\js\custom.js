/**
 * Custom JavaScript for Aphrodite theme
 */
jQuery(document).ready(function($) {
    // Smooth scroll for anchor links
    $('a[href*="#"]:not([href="#"])').click(function() {
        if (location.pathname.replace(/^\//, '') == this.pathname.replace(/^\//, '') && location.hostname == this.hostname) {
            var target = $(this.hash);
            target = target.length ? target : $('[name=' + this.hash.slice(1) + ']');
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 1000);
                return false;
            }
        }
    });

    // Add animation classes on scroll
    $(window).scroll(function() {
        $('.fade-in').each(function() {
            var elementTop = $(this).offset().top;
            var elementHeight = $(this).outerHeight();
            var windowHeight = $(window).height();
            var scrollY = $(window).scrollTop();
            
            if (scrollY > (elementTop - windowHeight + elementHeight/2)) {
                $(this).addClass('visible');
            }
        });
        
        $('.slide-up').each(function() {
            var elementTop = $(this).offset().top;
            var elementHeight = $(this).outerHeight();
            var windowHeight = $(window).height();
            var scrollY = $(window).scrollTop();
            
            if (scrollY > (elementTop - windowHeight + elementHeight/2)) {
                $(this).addClass('visible');
            }
        });
    });

    // Initialize lazy loading for background images
    $('.lazy-background').each(function() {
        var $this = $(this);
        var bgImage = $this.data('background');
        
        if (bgImage) {
            var img = new Image();
            img.onload = function() {
                $this.css('background-image', 'url(' + bgImage + ')');
                $this.addClass('loaded');
            };
            img.src = bgImage;
        }
    });

    // Modern Accordion FAQ interattivo
    $('.modern-accordion .accordion-header').on('click', function(){
        var item = $(this).closest('.accordion-item');
        var body = item.find('.accordion-body');
        if(item.hasClass('open')){
            body.slideUp(200);
            item.removeClass('open');
        }else{
            $('.modern-accordion .accordion-item.open .accordion-body').slideUp(200);
            $('.modern-accordion .accordion-item').removeClass('open');
            body.slideDown(200);
            item.addClass('open');
        }
    });
    // Chiudi tutte tranne la prima all'avvio
    $('.modern-accordion .accordion-item').not(':first').find('.accordion-body').hide();
    $('.modern-accordion .accordion-item').first().addClass('open');

    // MENU MOBILE HAMBURGER
    $('#mobile-hamburger').on('click', function(e){
        e.preventDefault();
        $('.main-navigation').toggleClass('open');
    });
    $(document).on('click', function(e){
        if (!$(e.target).closest('.main-navigation').length && !$(e.target).is('#mobile-hamburger')) {
            $('.main-navigation').removeClass('open');
        }
    });
});
