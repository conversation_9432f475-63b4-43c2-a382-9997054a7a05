<?php
/**
 * Aphrodite Theme Customizer
 *
 * @package Aphrodite
 */

/**
 * Add postMessage support for site title and description for the Theme Customizer.
 *
 * @param WP_Customize_Manager $wp_customize Theme Customizer object.
 */
function aphrodite_customize_register( $wp_customize ) {
	$wp_customize->get_setting( 'blogname' )->transport         = 'postMessage';
	$wp_customize->get_setting( 'blogdescription' )->transport  = 'postMessage';
	$wp_customize->get_setting( 'header_textcolor' )->transport = 'postMessage';

	if ( isset( $wp_customize->selective_refresh ) ) {
		$wp_customize->selective_refresh->add_partial(
			'blogname',
			array(
				'selector'        => '.site-title a',
				'render_callback' => 'aphrodite_customize_partial_blogname',
			)
		);
		$wp_customize->selective_refresh->add_partial(
			'blogdescription',
			array(
				'selector'        => '.site-description',
				'render_callback' => 'aphrodite_customize_partial_blogdescription',
			)
		);
	}

	// Footer Description
	$wp_customize->add_setting(
		'footer_description',
		array(
			'default'           => 'Un centro estetico elegante e professionale, dedicato alla tua bellezza e al tuo benessere.',
			'sanitize_callback' => 'sanitize_text_field',
			'transport'         => 'refresh',
		)
	);

	$wp_customize->add_control(
		'footer_description',
		array(
			'label'    => __( 'Descrizione Footer', 'aphrodite' ),
			'section'  => 'title_tagline',
			'type'     => 'textarea',
			'priority' => 30,
		)
	);

	// Social Media Links
	$wp_customize->add_section(
		'social_links',
		array(
			'title'       => __( 'Social Media', 'aphrodite' ),
			'description' => __( 'Aggiungi i link ai tuoi profili social', 'aphrodite' ),
			'priority'    => 120,
		)
	);

	$wp_customize->add_setting(
		'social_facebook',
		array(
			'default'           => '',
			'sanitize_callback' => 'esc_url_raw',
		)
	);

	$wp_customize->add_control(
		'social_facebook',
		array(
			'label'   => __( 'Facebook', 'aphrodite' ),
			'section' => 'social_links',
			'type'    => 'url',
		)
	);

	$wp_customize->add_setting(
		'social_instagram',
		array(
			'default'           => '',
			'sanitize_callback' => 'esc_url_raw',
		)
	);

	$wp_customize->add_control(
		'social_instagram',
		array(
			'label'   => __( 'Instagram', 'aphrodite' ),
			'section' => 'social_links',
			'type'    => 'url',
		)
	);

	$wp_customize->add_setting(
		'social_twitter',
		array(
			'default'           => '',
			'sanitize_callback' => 'esc_url_raw',
		)
	);

	$wp_customize->add_control(
		'social_twitter',
		array(
			'label'   => __( 'Twitter', 'aphrodite' ),
			'section' => 'social_links',
			'type'    => 'url',
		)
	);
}
add_action( 'customize_register', 'aphrodite_customize_register' );

/**
 * Render the site title for the selective refresh partial.
 *
 * @return void
 */
function aphrodite_customize_partial_blogname() {
	bloginfo( 'name' );
}

/**
 * Render the site tagline for the selective refresh partial.
 *
 * @return void
 */
function aphrodite_customize_partial_blogdescription() {
	bloginfo( 'description' );
}

/**
 * Binds JS handlers to make Theme Customizer preview reload changes asynchronously.
 */
function aphrodite_customize_preview_js() {
	wp_enqueue_script( 'aphrodite-customizer', get_template_directory_uri() . '/assets/js/customizer.js', array( 'customize-preview' ), APHRODITE_VERSION, true );
}
add_action( 'customize_preview_init', 'aphrodite_customize_preview_js' );
