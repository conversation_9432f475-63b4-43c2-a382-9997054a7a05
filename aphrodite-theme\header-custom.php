<?php
/**
 * <PERSON><PERSON> personalizzato per la home page
 *
 * @package Aphrodite
 */

?>
<!doctype html>
<html <?php language_attributes(); ?>>
<head>
	<meta charset="<?php bloginfo( 'charset' ); ?>">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="profile" href="https://gmpg.org/xfn/11">
	<!-- Font Awesome per le icone -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
	<?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>
<div id="page" class="site">
	<a class="skip-link screen-reader-text" href="#primary"><?php esc_html_e( 'Skip to content', 'aphrodite' ); ?></a>

	<header id="masthead" class="site-header">
		<div class="container">
			<div class="row align-center justify-between">
				<div class="site-branding">
					<?php
					if ( has_custom_logo() ) :
						the_custom_logo();
					else :
					?>
						<a href="<?php echo esc_url( home_url( '/' ) ); ?>" class="custom-logo-link">
							<img src="<?php echo get_template_directory_uri(); ?>/assets/images/logo.png" alt="<?php bloginfo( 'name' ); ?>" class="logo">
						</a>
					<?php endif; ?>
				</div>
				<nav id="site-navigation" class="main-navigation">
					<a href="#" class="mobile-hamburger" id="mobile-hamburger" aria-label="Apri menu"><i class="fas fa-bars"></i></a>
					<?php
					wp_nav_menu(
						array(
							'theme_location' => 'menu-1',
							'menu_id'        => 'primary-menu',
							'container'      => false,
							'fallback_cb'    => function() {
								echo '<ul>
									<li><a href="https://aphroditecenter.store/" class="active">Home</a></li>
									<li><a href="https://aphroditecenter.store/trattamenti/">Servizi</a></li>
									<li><a href="https://aphroditecenter.store/contatti/">Contatti</a></li>';
								if (current_user_can('edit_pages')) {
									echo '<li><a href="https://aphroditecenter.store/gestionale-bacoli/">Gestionale Bacoli</a></li>
									<li><a href="https://aphroditecenter.store/gestionale-monte-di-procida/">Gestionale Monte di Procida</a></li>';
								}
								echo '</ul>';
							},
						)
					);
					?>
				</nav>
			</div>
		</div>
	</header><!-- #masthead -->
