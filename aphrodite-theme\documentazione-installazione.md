# Documentazione per l'Installazione del Tema WordPress - Aphrodite Center Store

## Introduzione

Questa documentazione fornisce istruzioni dettagliate per installare e configurare il tema WordPress "Aphrodite Center Store", un tema elegante per centri estetici con colori oro chiaro e bianco.

Il tema è stato progettato specificamente per essere compatibile con Elementor (versione gratuita) e include tutte le configurazioni necessarie per creare un sito web professionale per il tuo centro estetico.

## Contenuto del Pacchetto

Il pacchetto del tema include:
- Tema WordPress completo "Aphrodite"
- File CSS personalizzati
- Configurazioni JSON per Elementor
- Documentazione dettagliata

## Requisiti di Sistema

- WordPress 5.0 o superiore
- PHP 7.4 o superiore
- Elementor (versione gratuita) per utilizzare le configurazioni JSON

## Installazione del Tema WordPress

### Metodo 1: Installazione tramite Dashboard WordPress

1. Accedi al pannello di amministrazione di WordPress
2. Vai a **Aspetto > Temi > Aggiungi nuovo**
3. Clicca su **Carica tema**
4. Seleziona il file zip del tema (`aphrodite-theme.zip`)
5. Clicca su **Installa ora**
6. Dopo l'installazione, clicca su **Attiva** per attivare il tema

### Metodo 2: Installazione tramite FTP

1. Estrai il file zip del tema sul tuo computer
2. Utilizzando un client FTP, carica la cartella `aphrodite-theme` nella directory `/wp-content/themes/` del tuo sito WordPress
3. Accedi al pannello di amministrazione di WordPress
4. Vai a **Aspetto > Temi**
5. Trova il tema "Aphrodite Center Store" e clicca su **Attiva**

## Struttura del Tema

Il tema è strutturato secondo gli standard WordPress:

```
aphrodite-theme/
├── assets/
│   ├── css/
│   │   ├── main.css (stili principali)
│   │   └── additional.css (effetti avanzati e ottimizzazioni)
│   ├── js/
│   │   ├── navigation.js
│   │   └── custom.js
│   └── images/
├── inc/
│   ├── customizer.php
│   ├── template-functions.php
│   └── template-tags.php
├── template-parts/
│   ├── content.php
│   └── content-none.php
├── functions.php
├── index.php
├── header.php
├── footer.php
├── sidebar.php
└── style.css
```

## Personalizzazione del Tema

### Personalizzazione tramite Customizer

1. Vai a **Aspetto > Personalizza**
2. Qui puoi modificare:
   - Logo del sito
   - Colori del tema
   - Immagine di sfondo
   - Widget di intestazione e piè di pagina
   - Menu di navigazione

### Utilizzo delle Configurazioni Elementor

Le configurazioni JSON di Elementor permettono di importare rapidamente layout preconfigurati:

1. Crea una nuova pagina o modifica una pagina esistente
2. Clicca sull'icona **Elementor** per avviare l'editor
3. Clicca sull'icona **Impostazioni pagina** (ingranaggio) nell'angolo in basso a sinistra
4. Vai alla scheda **Importa/Esporta**
5. Clicca su **Importa** e seleziona il file JSON desiderato

File JSON disponibili:
- `elementor-hero-section.json`: Sezione hero per la home page
- `elementor-servizi-section.json`: Sezione servizi
- `elementor-testimonial-section.json`: Sezione recensioni
- `elementor-contatti-section.json`: Sezione contatti

## Personalizzazione Avanzata

### Modifica dei CSS

I file CSS sono organizzati in due file principali:

1. **main.css**: Contiene gli stili di base, layout, componenti e responsive design
2. **additional.css**: Contiene effetti avanzati, miglioramenti per Elementor e ottimizzazioni

Per modificare questi file:
- Utilizza un editor di codice per modificare i file nella cartella `assets/css/`
- In alternativa, puoi aggiungere CSS personalizzato tramite il Customizer di WordPress

### Modifica dei Template

Per modificare la struttura delle pagine:
1. Crea una cartella `aphrodite-child` nella directory `/wp-content/themes/`
2. Crea un file `style.css` con l'intestazione appropriata per il tema child
3. Copia i file template che desideri modificare dal tema principale al tema child
4. Modifica i file nel tema child secondo le tue esigenze

## Risoluzione Problemi

### Problemi Comuni

- **Il tema non si installa**: Verifica che il file zip non sia danneggiato e che il tuo server soddisfi i requisiti minimi
- **I CSS non vengono caricati**: Controlla se ci sono conflitti con plugin di cache o ottimizzazione
- **Problemi di visualizzazione mobile**: Utilizza le classi CSS aggiuntive fornite nel file `additional.css`
- **Elementor non mostra le sezioni correttamente**: Assicurati di utilizzare la versione più recente di Elementor

### Supporto

Per assistenza tecnica, contatta il team di sviluppo all'indirizzo <EMAIL>

## Conclusione

Seguendo questa documentazione, sarai in grado di installare e configurare completamente il tema WordPress "Aphrodite Center Store" per il tuo centro estetico. Il tema è progettato per essere facile da usare e personalizzare, offrendo un'esperienza utente elegante e professionale.
