/*
Theme Name: Aphrodite Center Store
Theme URI: https://example.com/aphrodite-theme/
Author: Manus AI
Author URI: https://example.com/
Description: Un tema elegante per centri estetici con colori oro chiaro e bianco, progettato specificamente per Aphrodite Center Store.
Version: 1.0.0
Requires at least: 5.0
Tested up to: 6.3
Requires PHP: 7.4
License: GNU General Public License v2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html
Text Domain: aphrodite
Tags: beauty, spa, gold, white, elegant, responsive, elementor-ready
*/

/* Importazione dei file CSS */
@import url('assets/css/main.css');
@import url('assets/css/additional.css');

/* Stili di base per garantire la compatibilità con WordPress */
body {
    font-family: 'Open Sans', sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: #333333;
    background-color: #FFFFFF;
}

a {
    color: #D4AF37;
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: #B8860B;
}

img {
    max-width: 100%;
    height: auto;
}

.alignleft {
    float: left;
    margin-right: 1.5em;
    margin-bottom: 1.5em;
}

.alignright {
    float: right;
    margin-left: 1.5em;
    margin-bottom: 1.5em;
}

.aligncenter {
    clear: both;
    display: block;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 1.5em;
}

.wp-caption {
    margin-bottom: 1.5em;
    max-width: 100%;
}

.wp-caption img[class*="wp-image-"] {
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.wp-caption .wp-caption-text {
    margin: 0.8075em 0;
    font-size: 14px;
    font-style: italic;
}

.gallery {
    margin-bottom: 1.5em;
}

.gallery-item {
    display: inline-block;
    text-align: center;
    vertical-align: top;
    width: 100%;
}

.gallery-columns-2 .gallery-item {
    max-width: 50%;
}

.gallery-columns-3 .gallery-item {
    max-width: 33.33%;
}

.gallery-columns-4 .gallery-item {
    max-width: 25%;
}

.gallery-columns-5 .gallery-item {
    max-width: 20%;
}

.gallery-columns-6 .gallery-item {
    max-width: 16.66%;
}

.gallery-columns-7 .gallery-item {
    max-width: 14.28%;
}

.gallery-columns-8 .gallery-item {
    max-width: 12.5%;
}

.gallery-columns-9 .gallery-item {
    max-width: 11.11%;
}

.gallery-caption {
    display: block;
}

.screen-reader-text {
    border: 0;
    clip: rect(1px, 1px, 1px, 1px);
    clip-path: inset(50%);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute !important;
    width: 1px;
    word-wrap: normal !important;
}

.screen-reader-text:focus {
    background-color: #f1f1f1;
    border-radius: 3px;
    box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
    clip: auto !important;
    clip-path: none;
    color: #D4AF37;
    display: block;
    font-size: 14px;
    font-weight: bold;
    height: auto;
    left: 5px;
    line-height: normal;
    padding: 15px 23px 14px;
    text-decoration: none;
    top: 5px;
    width: auto;
    z-index: 100000;
}
