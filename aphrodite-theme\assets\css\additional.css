/**
 * Aphrodite Center Store - CSS Aggiuntivo
 * Stili specifici per migliorare l'integrazione con Elementor
 */

/* ======================================
   EFFETTI HOVER AVANZATI
====================================== */
.service-card:hover {
    transform: translateY(-8px);
    transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.gold-hover:hover {
    color: var(--oro-chiaro);
    transition: color 0.3s ease;
}

.image-zoom {
    overflow: hidden;
}

.image-zoom img {
    transition: transform 0.5s ease;
}

.image-zoom:hover img {
    transform: scale(1.05);
}

/* ======================================
   EFFETTI DECORATIVI
====================================== */
.gold-border-left {
    position: relative;
    padding-left: 20px;
}

.gold-border-left::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 3px;
    background-color: var(--oro-chiaro);
}

.gold-quote {
    position: relative;
    padding: 30px 40px;
}

.gold-quote::before {
    content: '"';
    font-family: 'Playfair Display', serif;
    font-size: 120px;
    color: rgba(212, 175, 55, 0.1);
    position: absolute;
    top: -20px;
    left: 20px;
    line-height: 1;
}

/* ======================================
   MIGLIORAMENTI ELEMENTOR
====================================== */
/* Fix per posizionamento assoluto in Elementor Free */
.elementor-absolute-fix {
    position: relative;
}

.elementor-absolute-fix .absolute-element {
    position: absolute;
    z-index: 1;
}

/* Miglioramenti per form in Elementor Free */
.elementor-form-enhanced .elementor-field {
    transition: all 0.3s ease !important;
}

.elementor-form-enhanced .elementor-field:focus {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(212, 175, 55, 0.15) !important;
}

.elementor-form-enhanced .elementor-button {
    position: relative;
    overflow: hidden;
}

.elementor-form-enhanced .elementor-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.elementor-form-enhanced .elementor-button:hover::before {
    left: 100%;
}

/* ======================================
   ANIMAZIONI AGGIUNTIVE
====================================== */
@keyframes goldPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(212, 175, 55, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(212, 175, 55, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(212, 175, 55, 0);
    }
}

.gold-pulse {
    animation: goldPulse 2s infinite;
}

@keyframes borderShine {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.border-shine {
    background: linear-gradient(90deg, var(--oro-chiaro), var(--oro-scuro), var(--oro-chiaro));
    background-size: 200% 200%;
    animation: borderShine 3s ease infinite;
}

/* ======================================
   OTTIMIZZAZIONI MOBILE AGGIUNTIVE
====================================== */
@media (max-width: 768px) {
    .mobile-center {
        text-align: center !important;
    }
    
    .mobile-order-1 {
        order: 1 !important;
    }
    
    .mobile-order-2 {
        order: 2 !important;
    }
    
    .mobile-mt-3 {
        margin-top: var(--spacing-md) !important;
    }
    
    .mobile-mb-3 {
        margin-bottom: var(--spacing-md) !important;
    }
    
    .mobile-p-2 {
        padding: var(--spacing-sm) !important;
    }
}

/* ======================================
   OTTIMIZZAZIONI PERFORMANCE
====================================== */
/* Lazy loading per immagini di sfondo */
.lazy-background {
    background-image: none !important;
    background-color: #f0f0f0;
    transition: background-image 0.5s ease-in;
}

.lazy-background.loaded {
    background-image: var(--background-image) !important;
}

/* Transizioni ottimizzate */
.optimize-gpu {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}
